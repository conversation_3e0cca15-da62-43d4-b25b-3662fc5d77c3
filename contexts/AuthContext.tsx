import { createContext } from 'react';
import type { Profile } from '@prisma/client';

interface AuthUser {
  id: string;
  email: string;
}

export interface AuthContextType {
  user: AuthUser | null;
  session: null; // Tidak perlu session di client
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null; user?: Profile }>;
  signUp: (email: string, password: string, fullName: string, token: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);