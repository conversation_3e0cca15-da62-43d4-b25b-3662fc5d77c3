'use client';
import { useState, useEffect, ReactNode } from 'react';
import { AuthContext } from './AuthContext';
import { AuthClientService } from '@/lib/auth-client';
import type { Profile } from '@prisma/client';

interface AuthUser {
  id: string;
  email: string;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const result = await AuthClientService.getProfile();
      if (result.user && result.profile) {
        setUser(result.user);
        setProfile(result.profile);
      }
    } catch (error) {
      console.error('Session check error:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const result = await AuthClientService.signIn(email, password);
      if (result.error) return { error: new Error(result.error) };

      if (result.user) {
        // Check session again to get updated user data
        await checkSession();
        return { error: null, user: result.user };
      }

      return { error: new Error('Login gagal') };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const signUp = async (email: string, password: string, fullName: string, token: string) => {
    try {
      const result = await AuthClientService.signUp(email, password, fullName, token);
      if (result.error) return { error: new Error(result.error) };
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const signOut = async () => {
    try {
      await AuthClientService.signOut();
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Signout error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      session: null, 
      profile, 
      loading, 
      signIn, 
      signUp, 
      signOut 
    }}>
      {children}
    </AuthContext.Provider>
  );
}