# <PERSON><PERSON><PERSON>s - RIASEC Career Assessment System

Sistem penilaian karir menggunakan metode RIASEC untuk membantu siswa memilih jurusan yang tepat.

## Tech Stack

- **Frontend**: React + TypeScript + Vite
- **UI**: Tailwind CSS + Shadcn/ui
- **Database**: MySQL + Prisma ORM
- **Authentication**: Custom auth system

## Setup Instructions

### Prerequisites
- Node.js & npm
- MySQL (accessible without password on port 3306)

### Installation

```sh
# Clone the repository
git clone <YOUR_GIT_URL>
cd jurusan-cerdas

# Install dependencies
npm install

# Setup database
mysql -u root -e "CREATE DATABASE IF NOT EXISTS jurasan_cerdas;"

# Run database migrations
npx prisma migrate dev

# Seed the database with initial data
npm run db:seed

# Start development server
npm run dev
```

## Database Setup

This project uses MySQL with Prisma ORM. The database schema includes:

- **profiles**: User profiles (Admin/Member roles)
- **tokens**: Registration tokens for member signup
- **assessment_results**: RIASEC assessment results

### Default Credentials

After running the seed script, you can use:

- **Admin**: <EMAIL>
- **Test Member**: <EMAIL>  
- **Sample Token**: sample-token-12345

## Features

- **Multi-role system**: Admin and Member roles
- **Token-based registration**: Members register using tokens provided by admin
- **RIASEC Assessment**: 24-question career interest assessment
- **Results visualization**: Detailed assessment results with recommendations
- **Admin dashboard**: Member and token management

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/805d836e-ab4f-4200-9080-d9cc7fc79d52) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/features/custom-domain#custom-domain)
