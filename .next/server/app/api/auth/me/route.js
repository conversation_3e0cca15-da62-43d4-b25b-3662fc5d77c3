"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/me/route";
exports.ids = ["app/api/auth/me/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_yoshuavictor_Nextjs_jurusan_cerdas_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/me/route.ts */ \"(rsc)/./app/api/auth/me/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/me/route\",\n        pathname: \"/api/auth/me\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/me/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Nextjs/jurusan-cerdas/app/api/auth/me/route.ts\",\n    nextConfigOutput,\n    userland: _Users_yoshuavictor_Nextjs_jurusan_cerdas_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/me/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/me/route.ts":
/*!**********************************!*\
  !*** ./app/api/auth/me/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\nasync function GET(request) {\n    try {\n        const sessionToken = request.cookies.get(\"session-token\")?.value;\n        if (!sessionToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Tidak ada session\"\n            }, {\n                status: 401\n            });\n        }\n        const session = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.getSession(sessionToken);\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Session tidak valid\"\n            }, {\n                status: 401\n            });\n        }\n        const profile = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.getProfile(session.user.id);\n        if (!profile) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Profile tidak ditemukan\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            user: session.user,\n            profile: profile\n        });\n    } catch (error) {\n        console.error(\"Me API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Terjadi kesalahan server\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/me/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n// Simple in-memory session store (in production, use Redis or similar)\nconst sessions = new Map();\nclass AuthService {\n    static async signUp(email, password, fullName, token) {\n        try {\n            // Validate token first\n            const tokenData = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findUnique({\n                where: {\n                    token\n                }\n            });\n            if (!tokenData) {\n                return {\n                    error: \"Token tidak valid\"\n                };\n            }\n            // Check if token is expired\n            if (tokenData.expired_at && new Date(tokenData.expired_at) < new Date()) {\n                return {\n                    error: \"Token sudah kadaluarsa\"\n                };\n            }\n            // Check if token has reached max uses\n            if (tokenData.current_uses >= tokenData.max_uses) {\n                return {\n                    error: \"Token sudah mencapai batas penggunaan maksimal\"\n                };\n            }\n            // Check if user already exists\n            const existingUser = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.findUnique({\n                where: {\n                    email\n                }\n            });\n            if (existingUser) {\n                return {\n                    error: \"Email sudah terdaftar\"\n                };\n            }\n            // Hash password\n            const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, 12);\n            // Create user profile\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.create({\n                data: {\n                    email,\n                    full_name: fullName,\n                    password: hashedPassword,\n                    role: \"MEMBER\"\n                }\n            });\n            // Update token usage\n            await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.update({\n                where: {\n                    id: tokenData.id\n                },\n                data: {\n                    current_uses: tokenData.current_uses + 1\n                }\n            });\n            // Store password separately (in production, you'd want a separate user auth table)\n            // For now, we'll skip password storage and assume external auth\n            return {\n                user\n            };\n        } catch (error) {\n            console.error(\"Signup error:\", error);\n            return {\n                error: \"Terjadi kesalahan saat mendaftar\"\n            };\n        }\n    }\n    static async signIn(email, password) {\n        try {\n            console.log(\"SignIn attempt for:\", email);\n            const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.findUnique({\n                where: {\n                    email\n                }\n            });\n            console.log(\"User found:\", user ? \"Yes\" : \"No\");\n            if (!user) {\n                return {\n                    error: \"Email atau password salah\"\n                };\n            }\n            // Verify password\n            const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, user.password);\n            console.log(\"Password valid:\", isPasswordValid);\n            if (!isPasswordValid) {\n                return {\n                    error: \"Email atau password salah\"\n                };\n            }\n            console.log(\"Creating session for user:\", user.id);\n            const sessionId = Math.random().toString(36).substring(2, 15);\n            const session = {\n                user: {\n                    id: user.id,\n                    email: user.email\n                },\n                accessToken: sessionId,\n                refreshToken: Math.random().toString(36).substring(2, 15),\n                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n            };\n            sessions.set(sessionId, session);\n            console.log(\"Session created successfully\");\n            return {\n                user,\n                session\n            };\n        } catch (error) {\n            console.error(\"Signin error details:\", error);\n            return {\n                error: `Terjadi kesalahan saat masuk: ${error instanceof Error ? error.message : \"Unknown error\"}`\n            };\n        }\n    }\n    static async signOut(sessionId) {\n        sessions.delete(sessionId);\n    }\n    static async getSession(sessionId) {\n        const session = sessions.get(sessionId);\n        if (!session) return null;\n        if (session.expiresAt < new Date()) {\n            sessions.delete(sessionId);\n            return null;\n        }\n        return session;\n    }\n    static async getProfile(userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.profile.findUnique({\n            where: {\n                id: userId\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFHO0FBRW5FLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2p1cnVzYW4tY2VyZGFzLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRXhwb3J0IHR5cGVzIGZyb20gUHJpc21hXG5leHBvcnQgdHlwZSB7XG4gIFByb2ZpbGUsXG4gIFRva2VuLFxuICBBc3Nlc3NtZW50UmVzdWx0LFxuICBVc2VyUm9sZSxcbn0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fyoshuavictor%2FNextjs%2Fjurusan-cerdas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();