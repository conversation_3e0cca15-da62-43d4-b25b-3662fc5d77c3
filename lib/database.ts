import { prisma } from "./prisma";

export class DatabaseService {
  // Profile operations
  static async getProfiles(role?: "ADMIN" | "MEMBER") {
    return await prisma.profile.findMany({
      where: role ? { role } : undefined,
      orderBy: { created_at: "desc" },
    });
  }

  static async getProfilesCount(role?: "ADMIN" | "MEMBER") {
    return await prisma.profile.count({
      where: role ? { role } : undefined,
    });
  }

  static async deleteProfile(id: string) {
    return await prisma.profile.delete({
      where: { id },
    });
  }

  // Token operations
  static async getTokens() {
    return await prisma.token.findMany({
      include: {
        created_by: {
          select: {
            full_name: true,
            email: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });
  }

  static async getTokensCount() {
    return await prisma.token.count();
  }

  static async getUsedTokensCount() {
    return await prisma.token.count({
      where: {
        current_uses: {
          gt: 0,
        },
      },
    });
  }

  static async createToken(data: {
    instansi: string;
    description?: string;
    created_by_id: string;
    max_uses: number;
    expired_at?: Date;
  }) {
    const token =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);

    return await prisma.token.create({
      data: {
        ...data,
        token,
      },
    });
  }

  static async deleteToken(id: string) {
    return await prisma.token.delete({
      where: { id },
    });
  }

  static async findTokenByValue(token: string) {
    return await prisma.token.findUnique({
      where: { token },
    });
  }

  // Assessment operations
  static async getAssessmentResults(userId?: string) {
    return await prisma.assessmentResult.findMany({
      where: userId ? { user_id: userId } : undefined,
      include: {
        user: {
          select: {
            full_name: true,
            email: true,
          },
        },
      },
      orderBy: { created_at: "desc" },
    });
  }

  static async getAssessmentResultsCount(userId?: string) {
    return await prisma.assessmentResult.count({
      where: userId ? { user_id: userId } : undefined,
    });
  }

  static async createAssessmentResult(data: {
    user_id: string;
    scores: Record<string, number>;
    strengths: string[];
    recommendations: string[];
  }) {
    return await prisma.assessmentResult.create({
      data: {
        ...data,
        scores: JSON.stringify(data.scores),
        strengths: JSON.stringify(data.strengths),
        recommendations: JSON.stringify(data.recommendations),
      },
    });
  }

  static async getLatestAssessmentResult(userId: string) {
    return await prisma.assessmentResult.findFirst({
      where: { user_id: userId },
      orderBy: { created_at: "desc" },
    });
  }
}
