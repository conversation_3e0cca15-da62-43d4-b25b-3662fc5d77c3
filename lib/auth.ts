import bcrypt from "bcryptjs";
import { prisma } from "./prisma";
import type { Profile, UserRole } from "@prisma/client";

export interface AuthUser {
  id: string;
  email: string;
}

export interface AuthSession {
  user: AuthUser;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

// Simple in-memory session store (in production, use Redis or similar)
const sessions = new Map<string, AuthSession>();

export class AuthService {
  static async signUp(
    email: string,
    password: string,
    fullName: string,
    token: string
  ): Promise<{ user?: Profile; error?: string }> {
    try {
      // Validate token first
      const tokenData = await prisma.token.findUnique({
        where: { token },
      });

      if (!tokenData) {
        return { error: "Token tidak valid" };
      }

      // Check if token is expired
      if (tokenData.expired_at && new Date(tokenData.expired_at) < new Date()) {
        return { error: "Token sudah kadaluarsa" };
      }

      // Check if token has reached max uses
      if (tokenData.current_uses >= tokenData.max_uses) {
        return { error: "Token sudah mencapai batas penggunaan maksimal" };
      }

      // Check if user already exists
      const existingUser = await prisma.profile.findUnique({
        where: { email },
      });

      if (existingUser) {
        return { error: "Email sudah terdaftar" };
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Create user profile
      const user = await prisma.profile.create({
        data: {
          email,
          full_name: fullName,
          password: hashedPassword,
          role: "MEMBER",
        },
      });

      // Update token usage
      await prisma.token.update({
        where: { id: tokenData.id },
        data: {
          current_uses: tokenData.current_uses + 1,
        },
      });

      // Store password separately (in production, you'd want a separate user auth table)
      // For now, we'll skip password storage and assume external auth

      return { user };
    } catch (error) {
      console.error("Signup error:", error);
      return { error: "Terjadi kesalahan saat mendaftar" };
    }
  }

  static async signIn(
    email: string,
    password: string
  ): Promise<{ user?: Profile; session?: AuthSession; error?: string }> {
    try {
      console.log("SignIn attempt for:", email);

      const user = await prisma.profile.findUnique({
        where: { email },
      });

      console.log("User found:", user ? "Yes" : "No");

      if (!user) {
        return { error: "Email atau password salah" };
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      console.log("Password valid:", isPasswordValid);

      if (!isPasswordValid) {
        return { error: "Email atau password salah" };
      }

      console.log("Creating session for user:", user.id);

      const sessionId = Math.random().toString(36).substring(2, 15);
      const session: AuthSession = {
        user: { id: user.id, email: user.email },
        accessToken: sessionId,
        refreshToken: Math.random().toString(36).substring(2, 15),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      };

      sessions.set(sessionId, session);
      console.log("Session created successfully");

      return { user, session };
    } catch (error) {
      console.error("Signin error details:", error);
      return {
        error: `Terjadi kesalahan saat masuk: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      };
    }
  }

  static async signOut(sessionId: string): Promise<void> {
    sessions.delete(sessionId);
  }

  static async getSession(sessionId: string): Promise<AuthSession | null> {
    const session = sessions.get(sessionId);
    if (!session) return null;

    if (session.expiresAt < new Date()) {
      sessions.delete(sessionId);
      return null;
    }

    return session;
  }

  static async getProfile(userId: string): Promise<Profile | null> {
    return await prisma.profile.findUnique({
      where: { id: userId },
    });
  }
}
