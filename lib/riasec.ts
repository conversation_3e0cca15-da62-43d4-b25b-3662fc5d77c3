// RIASEC Assessment System
// Based on Holland's Theory of Career Choice

export interface RIASECQuestion {
  id: number;
  text: string;
  category: "R" | "I" | "A" | "S" | "E" | "C";
}

export interface RIASECScores {
  realistic: number;
  investigative: number;
  artistic: number;
  social: number;
  enterprising: number;
  conventional: number;
}

export interface AssessmentResult {
  scores: RIASECScores;
  topStrengths: string[];
  recommendations: string[];
  description: string;
}

// Complete RIASEC Questions (60 questions total - 10 per category)
export const RIASEC_QUESTIONS: RIASECQuestion[] = [
  // Realistic (R) - 10 questions
  {
    id: 1,
    text: "Saya suka bekerja dengan mesin dan peralatan",
    category: "R",
  },
  {
    id: 2,
    text: "Saya menikmati pekerjaan yang melibatkan aktivitas fisik",
    category: "R",
  },
  {
    id: 3,
    text: "Saya suka membangun atau memperbaiki sesuatu dengan tangan",
    category: "R",
  },
  {
    id: 4,
    text: "Saya tertarik dengan bidang teknik dan engineering",
    category: "R",
  },
  { id: 5, text: "Saya lebih suka bekerja di luar ruangan", category: "R" },
  {
    id: 6,
    text: "Saya suka menggunakan alat-alat dan peralatan",
    category: "R",
  },
  {
    id: 7,
    text: "Saya menikmati aktivitas yang melibatkan keterampilan manual",
    category: "R",
  },
  {
    id: 8,
    text: "Saya tertarik dengan bidang pertanian atau peternakan",
    category: "R",
  },
  {
    id: 9,
    text: "Saya suka bekerja dengan hal-hal yang konkret dan nyata",
    category: "R",
  },
  {
    id: 10,
    text: "Saya menikmati pekerjaan yang menghasilkan produk fisik",
    category: "R",
  },

  // Investigative (I) - 10 questions
  {
    id: 11,
    text: "Saya suka melakukan eksperimen untuk mengetahui bagaimana cara kerja sesuatu",
    category: "I",
  },
  {
    id: 12,
    text: "Saya menikmati memecahkan masalah yang kompleks",
    category: "I",
  },
  { id: 13, text: "Saya tertarik dengan penelitian ilmiah", category: "I" },
  { id: 14, text: "Saya suka menganalisis data dan informasi", category: "I" },
  { id: 15, text: "Saya menikmati membaca artikel ilmiah", category: "I" },
  {
    id: 16,
    text: "Saya tertarik dengan bidang matematika dan sains",
    category: "I",
  },
  {
    id: 17,
    text: "Saya suka mencari tahu mengapa sesuatu terjadi",
    category: "I",
  },
  {
    id: 18,
    text: "Saya menikmati bekerja dengan teori dan konsep abstrak",
    category: "I",
  },
  { id: 19, text: "Saya tertarik dengan teknologi dan inovasi", category: "I" },
  {
    id: 20,
    text: "Saya suka mengembangkan hipotesis dan mengujinya",
    category: "I",
  },

  // Artistic (A) - 10 questions
  { id: 21, text: "Saya suka menciptakan karya seni", category: "A" },
  {
    id: 22,
    text: "Saya menikmati kegiatan kreatif seperti menulis atau menggambar",
    category: "A",
  },
  { id: 23, text: "Saya tertarik dengan musik dan pertunjukan", category: "A" },
  { id: 24, text: "Saya suka mengekspresikan ide-ide kreatif", category: "A" },
  { id: 25, text: "Saya menikmati desain dan estetika", category: "A" },
  {
    id: 26,
    text: "Saya tertarik dengan dunia fashion atau interior",
    category: "A",
  },
  {
    id: 27,
    text: "Saya suka berimajinasi dan berpikir out-of-the-box",
    category: "A",
  },
  { id: 28, text: "Saya menikmati fotografi atau videografi", category: "A" },
  {
    id: 29,
    text: "Saya tertarik dengan sastra dan karya tulis",
    category: "A",
  },
  {
    id: 30,
    text: "Saya suka menciptakan sesuatu yang unik dan original",
    category: "A",
  },

  // Social (S) - 10 questions
  {
    id: 31,
    text: "Saya suka membantu orang menyelesaikan masalah mereka",
    category: "S",
  },
  {
    id: 32,
    text: "Saya menikmati bekerja dengan anak-anak atau remaja",
    category: "S",
  },
  {
    id: 33,
    text: "Saya tertarik dengan bidang pendidikan dan pengajaran",
    category: "S",
  },
  {
    id: 34,
    text: "Saya suka memberikan dukungan emosional kepada orang lain",
    category: "S",
  },
  {
    id: 35,
    text: "Saya menikmati kegiatan sukarela dan pelayanan masyarakat",
    category: "S",
  },
  {
    id: 36,
    text: "Saya tertarik dengan bidang kesehatan dan perawatan",
    category: "S",
  },
  { id: 37, text: "Saya suka bekerja dalam tim dan kolaborasi", category: "S" },
  {
    id: 38,
    text: "Saya menikmati konseling dan memberikan nasihat",
    category: "S",
  },
  {
    id: 39,
    text: "Saya tertarik dengan perkembangan manusia dan psikologi",
    category: "S",
  },
  {
    id: 40,
    text: "Saya suka menciptakan lingkungan yang harmonis",
    category: "S",
  },

  // Enterprising (E) - 10 questions
  { id: 41, text: "Saya suka menjadi pemimpin", category: "E" },
  {
    id: 42,
    text: "Saya menikmati kegiatan menjual atau mempromosikan produk",
    category: "E",
  },
  {
    id: 43,
    text: "Saya tertarik dengan dunia bisnis dan entrepreneurship",
    category: "E",
  },
  {
    id: 44,
    text: "Saya suka mengorganisir dan mengelola proyek",
    category: "E",
  },
  { id: 45, text: "Saya menikmati negosiasi dan persuasi", category: "E" },
  {
    id: 46,
    text: "Saya tertarik dengan investasi dan keuangan",
    category: "E",
  },
  {
    id: 47,
    text: "Saya suka mengambil risiko untuk mencapai tujuan",
    category: "E",
  },
  {
    id: 48,
    text: "Saya menikmati public speaking dan presentasi",
    category: "E",
  },
  {
    id: 49,
    text: "Saya tertarik dengan strategi dan perencanaan bisnis",
    category: "E",
  },
  {
    id: 50,
    text: "Saya suka mempengaruhi dan memotivasi orang lain",
    category: "E",
  },

  // Conventional (C) - 10 questions
  { id: 51, text: "Saya suka bekerja dengan angka dan data", category: "C" },
  {
    id: 52,
    text: "Saya menikmati pekerjaan yang terstruktur dan terorganisir",
    category: "C",
  },
  {
    id: 53,
    text: "Saya tertarik dengan akuntansi dan administrasi",
    category: "C",
  },
  {
    id: 54,
    text: "Saya suka mengikuti prosedur dan aturan yang jelas",
    category: "C",
  },
  { id: 55, text: "Saya menikmati pekerjaan detail dan teliti", category: "C" },
  {
    id: 56,
    text: "Saya tertarik dengan sistem komputer dan database",
    category: "C",
  },
  { id: 57, text: "Saya suka menganalisis laporan keuangan", category: "C" },
  {
    id: 58,
    text: "Saya menikmati pekerjaan yang membutuhkan keakuratan tinggi",
    category: "C",
  },
  {
    id: 59,
    text: "Saya tertarik dengan bidang hukum dan peraturan",
    category: "C",
  },
  {
    id: 60,
    text: "Saya suka mengelola informasi dan dokumentasi",
    category: "C",
  },
];

// Calculate RIASEC scores from answers
export function calculateScores(answers: Record<number, number>): RIASECScores {
  const scores: RIASECScores = {
    realistic: 0,
    investigative: 0,
    artistic: 0,
    social: 0,
    enterprising: 0,
    conventional: 0,
  };

  RIASEC_QUESTIONS.forEach((question) => {
    const answer = answers[question.id] || 0;

    switch (question.category) {
      case "R":
        scores.realistic += answer;
        break;
      case "I":
        scores.investigative += answer;
        break;
      case "A":
        scores.artistic += answer;
        break;
      case "S":
        scores.social += answer;
        break;
      case "E":
        scores.enterprising += answer;
        break;
      case "C":
        scores.conventional += answer;
        break;
    }
  });

  // Convert to percentage (max score per category is 50: 10 questions × 5 points)
  const maxScore = 50;
  scores.realistic = Math.round((scores.realistic / maxScore) * 100);
  scores.investigative = Math.round((scores.investigative / maxScore) * 100);
  scores.artistic = Math.round((scores.artistic / maxScore) * 100);
  scores.social = Math.round((scores.social / maxScore) * 100);
  scores.enterprising = Math.round((scores.enterprising / maxScore) * 100);
  scores.conventional = Math.round((scores.conventional / maxScore) * 100);

  return scores;
}

// Get top 3 strengths based on scores
export function getTopStrengths(scores: RIASECScores): string[] {
  const categoryNames = {
    realistic: "Realistic",
    investigative: "Investigative",
    artistic: "Artistic",
    social: "Social",
    enterprising: "Enterprising",
    conventional: "Conventional",
  };

  const sortedScores = Object.entries(scores)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([category]) => categoryNames[category as keyof typeof categoryNames]);

  return sortedScores;
}

// Get major recommendations based on top strengths
export function getRecommendations(topStrengths: string[]): string[] {
  const recommendations: Record<string, string[]> = {
    Realistic: [
      "Teknik Sipil",
      "Teknik Mesin",
      "Teknik Elektro",
      "Arsitektur",
      "Pertanian",
      "Kehutanan",
      "Teknologi Pangan",
    ],
    Investigative: [
      "Kedokteran",
      "Farmasi",
      "Matematika",
      "Fisika",
      "Kimia",
      "Biologi",
      "Teknik Informatika",
      "Statistika",
    ],
    Artistic: [
      "Desain Grafis",
      "Desain Interior",
      "Seni Rupa",
      "Musik",
      "Sastra",
      "Komunikasi Visual",
      "Film dan Televisi",
      "Fashion Design",
    ],
    Social: [
      "Psikologi",
      "Pendidikan",
      "Keperawatan",
      "Kesehatan Masyarakat",
      "Pekerjaan Sosial",
      "Hubungan Internasional",
      "Sosiologi",
      "Antropologi",
    ],
    Enterprising: [
      "Manajemen",
      "Ekonomi",
      "Akuntansi",
      "Marketing",
      "Administrasi Bisnis",
      "Kewirausahaan",
      "Hubungan Masyarakat",
      "Hukum",
    ],
    Conventional: [
      "Akuntansi",
      "Administrasi Negara",
      "Sistem Informasi",
      "Perpajakan",
      "Perbankan",
      "Manajemen Keuangan",
      "Ilmu Komputer",
      "Statistika",
    ],
  };

  const allRecommendations = new Set<string>();

  // Get recommendations from top 3 strengths
  topStrengths.forEach((strength) => {
    const majors = recommendations[strength] || [];
    majors.forEach((major) => allRecommendations.add(major));
  });

  // Return unique recommendations, limited to 8
  return Array.from(allRecommendations).slice(0, 8);
}

// Get description for RIASEC profile
export function getProfileDescription(topStrengths: string[]): string {
  const descriptions: Record<string, string> = {
    Realistic:
      "Anda menyukai pekerjaan praktis yang melibatkan aktivitas fisik dan penggunaan alat. Anda cenderung pragmatis dan menikmati hasil kerja yang konkret.",
    Investigative:
      "Anda tertarik pada penelitian, analisis, dan pemecahan masalah kompleks. Anda menikmati berpikir kritis dan bekerja dengan ide-ide abstrak.",
    Artistic:
      "Anda kreatif dan menikmati mengekspresikan diri melalui berbagai bentuk seni. Anda menghargai keindahan dan originalitas.",
    Social:
      "Anda peduli dengan orang lain dan menikmati membantu, mengajar, atau memberikan dukungan. Anda bekerja dengan baik dalam lingkungan kolaboratif.",
    Enterprising:
      "Anda natural leader yang menikmati tantangan bisnis, persuasi, dan pencapaian tujuan. Anda berani mengambil risiko untuk kesuksesan.",
    Conventional:
      "Anda menikmati pekerjaan yang terorganisir, detail, dan mengikuti prosedur yang jelas. Anda handal dalam mengolah data dan informasi.",
  };

  const primary = topStrengths[0];
  return (
    descriptions[primary] ||
    "Anda memiliki kombinasi unik dari berbagai karakteristik kepribadian yang dapat membuka banyak peluang karir."
  );
}

// Complete assessment result generator
export function generateAssessmentResult(
  answers: Record<number, number>
): AssessmentResult {
  const scores = calculateScores(answers);
  const topStrengths = getTopStrengths(scores);
  const recommendations = getRecommendations(topStrengths);
  const description = getProfileDescription(topStrengths);

  return {
    scores,
    topStrengths,
    recommendations,
    description,
  };
}
