import type { Profile } from "@prisma/client";

export interface AuthUser {
  id: string;
  email: string;
}

export interface AuthSession {
  user: AuthUser;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export class AuthClientService {
  static async signIn(
    email: string,
    password: string
  ): Promise<{
    user?: Profile;
    error?: string;
  }> {
    try {
      const response = await fetch("/api/auth/signin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || "Login gagal" };
      }

      return { user: data.user };
    } catch (error) {
      return { error: "Terjadi kesalahan saat login" };
    }
  }

  static async signUp(
    email: string,
    password: string,
    fullName: string,
    token: string
  ): Promise<{
    user?: Profile;
    error?: string;
  }> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password, fullName, token }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || "Registrasi gagal" };
      }

      return { user: data.user };
    } catch (error) {
      return { error: "Terjadi kesalahan saat registrasi" };
    }
  }

  static async signOut(): Promise<void> {
    try {
      await fetch("/api/auth/signout", {
        method: "POST",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }
  }

  static async getProfile(): Promise<{
    user?: AuthUser;
    profile?: Profile;
    error?: string;
  }> {
    try {
      const response = await fetch("/api/auth/me");

      if (!response.ok) {
        return { error: "Tidak ada session aktif" };
      }

      const data = await response.json();
      return { user: data.user, profile: data.profile };
    } catch (error) {
      return { error: "Terjadi kesalahan saat mengambil profile" };
    }
  }
}
