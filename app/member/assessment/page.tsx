'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/hooks/useAuth';
import AuthGuard from '@/components/AuthGuard';
import { toast } from 'sonner';
import { RIASEC_QUESTIONS, generateAssessmentResult } from '@/lib/riasec';

export default function AssessmentPage() {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [submitting, setSubmitting] = useState(false);
  const { profile } = useAuth();
  const router = useRouter();

  const handleAnswer = (value: string) => {
    setAnswers({ ...answers, [RIASEC_QUESTIONS[currentQuestion].id]: parseInt(value) });
  };

  const handleNext = () => {
    if (!answers[RIASEC_QUESTIONS[currentQuestion].id]) {
      toast.error('Mohon pilih jawaban terlebih dahulu');
      return;
    }
    
    if (currentQuestion === RIASEC_QUESTIONS.length - 1) {
      handleSubmit();
    } else {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentQuestion(currentQuestion - 1);
  };

  const handleSubmit = async () => {
    if (!answers[RIASEC_QUESTIONS[currentQuestion].id]) {
      toast.error('Mohon pilih jawaban terlebih dahulu');
      return;
    }

    if (!profile) {
      toast.error('Anda harus login untuk menyimpan hasil');
      return;
    }

    setSubmitting(true);

    try {
      // Calculate RIASEC scores and generate complete result
      const result = generateAssessmentResult(answers);

      // TODO: Save assessment result to database
      console.log('Assessment result:', result);

      toast.success('Assessment berhasil diselesaikan!');
      router.push('/member/results');
    } catch (error) {
      console.error('Error saving assessment:', error);
      toast.error('Gagal menyimpan hasil assessment');
    } finally {
      setSubmitting(false);
    }
  };

  const progress = ((currentQuestion + 1) / RIASEC_QUESTIONS.length) * 100;
  const currentQ = RIASEC_QUESTIONS[currentQuestion];

  return (
    <AuthGuard requiredRole="MEMBER">
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
        <div className="container mx-auto max-w-4xl">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Assessment RIASEC</h1>
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Pertanyaan {currentQuestion + 1} dari {RIASEC_QUESTIONS.length}</span>
                <span>{Math.round(progress)}% selesai</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </div>

          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="text-xl">
                Pertanyaan {currentQuestion + 1}
              </CardTitle>
              <CardDescription>
                Pilih jawaban yang paling sesuai dengan diri Anda
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-lg font-medium">{currentQ.text}</p>
              
              <RadioGroup
                value={answers[currentQ.id]?.toString() || ""}
                onValueChange={handleAnswer}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="1" id="option-1" />
                  <Label htmlFor="option-1" className="cursor-pointer">
                    Sangat tidak setuju
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="2" id="option-2" />
                  <Label htmlFor="option-2" className="cursor-pointer">
                    Tidak setuju
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="3" id="option-3" />
                  <Label htmlFor="option-3" className="cursor-pointer">
                    Netral
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="4" id="option-4" />
                  <Label htmlFor="option-4" className="cursor-pointer">
                    Setuju
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="5" id="option-5" />
                  <Label htmlFor="option-5" className="cursor-pointer">
                    Sangat setuju
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          <div className="flex justify-between">
            <Button 
              variant="outline" 
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
            >
              Sebelumnya
            </Button>
            
            <Button 
              onClick={handleNext}
              disabled={submitting}
            >
              {submitting ? 'Menyimpan...' : 
               currentQuestion === RIASEC_QUESTIONS.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}