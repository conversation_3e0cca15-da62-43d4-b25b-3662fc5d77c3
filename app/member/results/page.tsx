'use client';
import { useEffect, useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/hooks/useAuth';
import AuthGuard from '@/components/AuthGuard';
import Link from 'next/link';
import { BarChart3, Download, RefreshCw } from 'lucide-react';

type RIASECResult = {
  id: string;
  realistic: number;
  investigative: number;
  artistic: number;
  social: number;
  enterprising: number;
  conventional: number;
  topStrengths: string[];
  recommendations: string[];
  createdAt: Date;
};

export default function ResultsPage() {
  const [results, setResults] = useState<RIASECResult[]>([]);
  const [loading, setLoading] = useState(true);
  const { profile } = useAuth();

  const fetchResults = useCallback(async () => {
    if (!profile) return;

    try {
      setLoading(true);
      // TODO: Implement actual API call
      // const data = await DatabaseService.getAssessmentResults(profile.id);
      // setResults(data);

      // Dummy data for now
      setResults([
        {
          id: '1',
          realistic: 75,
          investigative: 60,
          artistic: 45,
          social: 80,
          enterprising: 55,
          conventional: 40,
          topStrengths: ['Social', 'Realistic', 'Investigative'],
          recommendations: [
            'Pendidikan Guru',
            'Psikologi',
            'Teknik Sipil',
            'Kedokteran'
          ],
          createdAt: new Date()
        }
      ]);
    } catch (error) {
      console.error('Error fetching results:', error);
    } finally {
      setLoading(false);
    }
  }, [profile]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  if (loading) {
    return (
      <AuthGuard requiredRole="MEMBER">
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </AuthGuard>
    );
  }

  if (results.length === 0) {
    return (
      <AuthGuard requiredRole="MEMBER">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center py-16">
              <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Belum Ada Hasil Assessment
              </h2>
              <p className="text-gray-600 mb-8">
                Anda belum menyelesaikan assessment RIASEC. Mulai sekarang untuk mendapatkan rekomendasi jurusan yang tepat.
              </p>
              <Link href="/member/assessment">
                <Button size="lg">
                  Mulai Assessment
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </AuthGuard>
    );
  }

  const latestResult = results[0];
  const riasecData = [
    { name: 'Realistic', value: latestResult.realistic, color: 'bg-blue-500' },
    { name: 'Investigative', value: latestResult.investigative, color: 'bg-green-500' },
    { name: 'Artistic', value: latestResult.artistic, color: 'bg-purple-500' },
    { name: 'Social', value: latestResult.social, color: 'bg-yellow-500' },
    { name: 'Enterprising', value: latestResult.enterprising, color: 'bg-red-500' },
    { name: 'Conventional', value: latestResult.conventional, color: 'bg-gray-500' },
  ];

  return (
    <AuthGuard requiredRole="MEMBER">
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
        <div className="container mx-auto max-w-6xl">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Hasil Assessment RIASEC</h1>
              <p className="text-gray-600 mt-2">
                Hasil terakhir: {latestResult.createdAt.toLocaleDateString('id-ID')}
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={fetchResults}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* RIASEC Scores */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Skor RIASEC Anda</CardTitle>
                  <CardDescription>
                    Grafik menunjukkan kesesuaian Anda dengan setiap tipe kepribadian RIASEC
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {riasecData.map((item) => (
                    <div key={item.name} className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium">{item.name}</span>
                        <span className="text-sm text-gray-600">{item.value}/100</span>
                      </div>
                      <Progress value={item.value} className="h-3" />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Top Strengths */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Kekuatan Utama</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {latestResult.topStrengths.map((strength, index) => (
                      <Badge key={index} variant="default">
                        {strength}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Rekomendasi Jurusan</CardTitle>
                  <CardDescription>
                    Jurusan yang sesuai dengan profil RIASEC Anda
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {latestResult.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle>Ulangi Assessment</CardTitle>
                <CardDescription>
                  Lakukan assessment ulang untuk memperbarui hasil Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Link href="/member/assessment">
                  <Button className="w-full">
                    Ulangi Assessment
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle>Konsultasi Lanjutan</CardTitle>
                <CardDescription>
                  Dapatkan konsultasi lebih mendalam tentang pilihan karir Anda
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Hubungi Konselor
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}