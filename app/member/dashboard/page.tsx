'use client';
import { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ClipboardList, FileText, LogOut } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { DatabaseService } from '@/lib/database';

export default function MemberDashboardPage() {
  const { profile, signOut } = useAuth();
  const [assessmentCount, setAssessmentCount] = useState(0);
  const router = useRouter();

  const fetchAssessmentCount = useCallback(async () => {
    if (!profile) return;

    try {
      const count = await DatabaseService.getAssessmentResultsCount(profile.id);
      setAssessmentCount(count);
    } catch (error) {
      console.error('Failed to fetch assessment count:', error);
    }
  }, [profile]);

  useEffect(() => {
    fetchAssessmentCount();
  }, [fetchAssessmentCount]);

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard Member</h1>
            <p className="text-gray-600 mt-2">Selamat datang, {profile.full_name}!</p>
          </div>
          <Button onClick={handleSignOut} variant="outline" className="flex items-center gap-2">
            <LogOut className="h-4 w-4" />
            Keluar
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assessment</CardTitle>
              <ClipboardList className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assessmentCount}</div>
              <p className="text-xs text-muted-foreground">Assessment yang telah diselesaikan</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Aktif</div>
              <p className="text-xs text-muted-foreground">Member aktif</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Hasil Tersedia</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assessmentCount}</div>
              <p className="text-xs text-muted-foreground">Hasil yang dapat dilihat</p>
            </CardContent>
          </Card>
        </div>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ClipboardList className="h-5 w-5" />
                Mulai Assessment
              </CardTitle>
              <CardDescription>
                Ikuti assessment RIASEC untuk mengetahui kepribadian dan minat Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/member/assessment">
                <Button className="w-full">
                  Mulai Assessment RIASEC
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Lihat Hasil
              </CardTitle>
              <CardDescription>
                Lihat hasil assessment dan rekomendasi jurusan yang cocok untuk Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/member/results">
                <Button variant="outline" className="w-full" disabled={assessmentCount === 0}>
                  {assessmentCount === 0 ? 'Belum Ada Hasil' : 'Lihat Hasil Assessment'}
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}