'use client';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Landing from '@/components/Landing';

export default function HomePage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      // Redirect ke dashboard berdasarkan role
      const profile = user as { role?: string };
      if (profile.role === 'ADMIN') {
        router.push('/admin/dashboard');
      } else if (profile.role === 'MEMBER') {
        router.push('/member/dashboard');
      }
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Jika belum login, tampilkan landing page
  return <Landing />;
}