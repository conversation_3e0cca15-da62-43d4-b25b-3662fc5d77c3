'use client';
import { useEffect, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Copy, Trash2, Edit2 } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import AuthGuard from '@/components/AuthGuard';
import { toast } from 'sonner';

type Token = {
  id: string;
  token: string;
  instansi: string;
  description: string | null;
  maxUses: number;
  currentUses: number;
  expiredAt: Date | null;
  createdAt: Date;
};

export default function TokenManagementPage() {
  const [tokens, setTokens] = useState<Token[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [formData, setFormData] = useState({
    instansi: '',
    description: '',
    maxUses: 10,
    expiredAt: ''
  });
  const { profile } = useAuth();

  const fetchTokens = useCallback(async () => {
    if (!profile) return;

    try {
      setLoading(true);
      const response = await fetch('/api/admin/tokens');
      if (!response.ok) {
        throw new Error('Failed to fetch tokens');
      }
      const data = await response.json();

      // Convert date strings to Date objects
      const formattedTokens = data.map((token: {
        id: string;
        token: string;
        instansi: string;
        description: string | null;
        maxUses: number;
        currentUses: number;
        expiredAt: string | null;
        createdAt: string;
        createdBy: string;
      }) => ({
        ...token,
        createdAt: new Date(token.createdAt),
        expiredAt: token.expiredAt ? new Date(token.expiredAt) : null
      }));

      setTokens(formattedTokens);
    } catch (error) {
      console.error('Failed to fetch tokens:', error);
      toast.error('Gagal memuat data token');
    } finally {
      setLoading(false);
    }
  }, [profile]);

  useEffect(() => {
    fetchTokens();
  }, [fetchTokens]);

  const handleCreateToken = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.instansi.trim()) {
      toast.error('Nama instansi harus diisi');
      return;
    }

    try {
      if (!profile?.id) {
        toast.error('User tidak terautentikasi');
        return;
      }

      const tokenData = {
        instansi: formData.instansi,
        description: formData.description || undefined,
        created_by_id: profile.id,
        max_uses: formData.maxUses,
        expired_at: formData.expiredAt ? formData.expiredAt : undefined
      };

      const response = await fetch('/api/admin/tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tokenData),
      });

      if (!response.ok) {
        throw new Error('Failed to create token');
      }

      toast.success('Token berhasil dibuat');
      setShowCreateDialog(false);
      setFormData({ instansi: '', description: '', maxUses: 10, expiredAt: '' });
      fetchTokens();
    } catch (error) {
      console.error('Error creating token:', error);
      toast.error('Gagal membuat token');
    }
  };

  const handleCopyToken = (token: string) => {
    navigator.clipboard.writeText(token);
    toast.success('Token berhasil disalin');
  };

  const handleDeleteToken = async (tokenId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus token ini?')) return;

    try {
      const response = await fetch(`/api/admin/tokens?id=${tokenId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete token');
      }

      toast.success('Token berhasil dihapus');
      fetchTokens();
    } catch (error) {
      console.error('Error deleting token:', error);
      toast.error('Gagal menghapus token');
    }
  };

  const getTokenStatus = (token: Token) => {
    if (token.expiredAt && new Date() > token.expiredAt) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (token.currentUses >= token.maxUses) {
      return <Badge variant="secondary">Habis</Badge>;
    }
    return <Badge variant="default">Aktif</Badge>;
  };

  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
        <div className="container mx-auto max-w-6xl">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Kelola Token</h1>
              <p className="text-gray-600 mt-2">Buat dan kelola token pendaftaran untuk member baru</p>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Buat Token Baru
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Buat Token Baru</DialogTitle>
                  <DialogDescription>
                    Isi form di bawah ini untuk membuat token pendaftaran baru
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateToken} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="instansi">Nama Instansi</Label>
                    <Input
                      id="instansi"
                      value={formData.instansi}
                      onChange={(e) => setFormData({ ...formData, instansi: e.target.value })}
                      placeholder="Contoh: Universitas ABC"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Deskripsi (Opsional)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Deskripsi penggunaan token"
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="maxUses">Maksimal Penggunaan</Label>
                      <Input
                        id="maxUses"
                        type="number"
                        min="1"
                        value={formData.maxUses}
                        onChange={(e) => setFormData({ ...formData, maxUses: parseInt(e.target.value) || 10 })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="expiredAt">Tanggal Kadaluwarsa (Opsional)</Label>
                      <Input
                        id="expiredAt"
                        type="date"
                        value={formData.expiredAt}
                        onChange={(e) => setFormData({ ...formData, expiredAt: e.target.value })}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Batal
                    </Button>
                    <Button type="submit">
                      Buat Token
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Tokens Table */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Token</CardTitle>
              <CardDescription>
                Token yang telah dibuat untuk pendaftaran member baru
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : tokens.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Belum ada token yang dibuat</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Token</TableHead>
                      <TableHead>Instansi</TableHead>
                      <TableHead>Penggunaan</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Dibuat</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tokens.map((token) => (
                      <TableRow key={token.id}>
                        <TableCell className="font-mono">
                          <div className="flex items-center gap-2">
                            <span className="truncate max-w-32">{token.token}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleCopyToken(token.token)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{token.instansi}</p>
                            {token.description && (
                              <p className="text-sm text-gray-500 truncate max-w-48">
                                {token.description}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {token.currentUses}/{token.maxUses}
                        </TableCell>
                        <TableCell>
                          {getTokenStatus(token)}
                        </TableCell>
                        <TableCell>
                          {token.createdAt.toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button size="sm" variant="ghost">
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDeleteToken(token.id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthGuard>
  );
}