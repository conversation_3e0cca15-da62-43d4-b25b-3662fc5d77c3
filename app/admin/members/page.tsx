'use client';
import { useEffect, useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Eye, Trash2, Download } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import AuthGuard from '@/components/AuthGuard';
import { toast } from 'sonner';

type Member = {
  id: string;
  email: string;
  fullName: string;
  createdAt: Date;
  assessmentCount: number;
  lastActivity: Date | null;
  isActive: boolean;
};

export default function MemberManagementPage() {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const { profile } = useAuth();

  const fetchMembers = useCallback(async () => {
    if (!profile) return;

    try {
      setLoading(true);
      const response = await fetch('/api/admin/members');
      if (!response.ok) {
        throw new Error('Failed to fetch members');
      }
      const data = await response.json();

      // Convert date strings to Date objects
      const formattedMembers = data.map((member: {
        id: string;
        email: string;
        fullName: string;
        createdAt: string;
        assessmentCount: number;
        lastActivity: string | null;
        isActive: boolean;
      }) => ({
        ...member,
        createdAt: new Date(member.createdAt),
        lastActivity: member.lastActivity ? new Date(member.lastActivity) : null
      }));

      setMembers(formattedMembers);
    } catch (error) {
      console.error('Failed to fetch members:', error);
      toast.error('Gagal memuat data member');
    } finally {
      setLoading(false);
    }
  }, [profile]);

  useEffect(() => {
    fetchMembers();
  }, [fetchMembers]);

  const handleDeleteMember = async (memberId: string, memberName: string) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus member "${memberName}"?`)) return;

    try {
      const response = await fetch(`/api/admin/members?id=${memberId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete member');
      }

      toast.success('Member berhasil dihapus');
      fetchMembers();
    } catch (error) {
      console.error('Error deleting member:', error);
      toast.error('Gagal menghapus member');
    }
  };

  const handleExportData = () => {
    // TODO: Implement actual export functionality
    toast.success('Data member berhasil diekspor');
  };

  const filteredMembers = members.filter(member =>
    member.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getActivityStatus = (member: Member) => {
    if (!member.isActive) {
      return <Badge variant="secondary">Tidak Aktif</Badge>;
    }
    
    if (!member.lastActivity) {
      return <Badge variant="outline">Belum Aktif</Badge>;
    }
    
    const daysSinceActivity = Math.floor(
      (new Date().getTime() - member.lastActivity.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceActivity <= 7) {
      return <Badge variant="default">Aktif</Badge>;
    } else if (daysSinceActivity <= 30) {
      return <Badge variant="secondary">Kurang Aktif</Badge>;
    } else {
      return <Badge variant="outline">Tidak Aktif</Badge>;
    }
  };

  if (!profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-8">
        <div className="container mx-auto max-w-6xl">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Kelola Member</h1>
              <p className="text-gray-600 mt-2">Lihat dan kelola data member yang terdaftar</p>
            </div>
            <Button onClick={handleExportData} variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Ekspor Data
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Total Member</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{members.length}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Member Aktif</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {members.filter(m => m.isActive).length}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Sudah Assessment</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {members.filter(m => m.assessmentCount > 0).length}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Belum Assessment</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {members.filter(m => m.assessmentCount === 0).length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filter */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Cari member berdasarkan nama atau email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="max-w-sm"
                />
              </div>
            </CardContent>
          </Card>

          {/* Members Table */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Member</CardTitle>
              <CardDescription>
                {filteredMembers.length} dari {members.length} member ditampilkan
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : filteredMembers.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">
                    {searchQuery ? 'Tidak ada member yang sesuai dengan pencarian' : 'Belum ada member yang terdaftar'}
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Assessment</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Bergabung</TableHead>
                      <TableHead>Aktivitas Terakhir</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell className="font-medium">
                          {member.fullName}
                        </TableCell>
                        <TableCell>{member.email}</TableCell>
                        <TableCell>
                          <Badge variant={member.assessmentCount > 0 ? "default" : "secondary"}>
                            {member.assessmentCount}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {getActivityStatus(member)}
                        </TableCell>
                        <TableCell>
                          {member.createdAt.toLocaleDateString('id-ID')}
                        </TableCell>
                        <TableCell>
                          {member.lastActivity 
                            ? member.lastActivity.toLocaleDateString('id-ID')
                            : 'Belum ada'
                          }
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button size="sm" variant="ghost">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleDeleteMember(member.id, member.fullName)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthGuard>
  );
}