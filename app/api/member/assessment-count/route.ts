import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const count = await DatabaseService.getAssessmentResultsCount(userId);

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Failed to fetch assessment count:', error);
    return NextResponse.json(
      { error: 'Failed to fetch assessment count' },
      { status: 500 }
    );
  }
}
