import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET() {
  try {
    const tokens = await DatabaseService.getTokens();
    
    const formattedTokens = tokens.map(token => ({
      id: token.id,
      token: token.token,
      instansi: token.instansi,
      description: token.description,
      maxUses: token.max_uses,
      currentUses: token.current_uses,
      expiredAt: token.expired_at,
      createdAt: token.created_at,
      createdBy: token.created_by?.full_name || 'Unknown'
    }));

    return NextResponse.json(formattedTokens);
  } catch (error) {
    console.error('Failed to fetch tokens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tokens' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { instansi, description, created_by_id, max_uses, expired_at } = body;

    if (!instansi || !created_by_id || !max_uses) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const tokenData = {
      instansi,
      description,
      created_by_id,
      max_uses,
      expired_at: expired_at ? new Date(expired_at) : undefined
    };

    const newToken = await DatabaseService.createToken(tokenData);

    return NextResponse.json(newToken);
  } catch (error) {
    console.error('Failed to create token:', error);
    return NextResponse.json(
      { error: 'Failed to create token' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenId = searchParams.get('id');

    if (!tokenId) {
      return NextResponse.json(
        { error: 'Token ID is required' },
        { status: 400 }
      );
    }

    await DatabaseService.deleteToken(tokenId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete token:', error);
    return NextResponse.json(
      { error: 'Failed to delete token' },
      { status: 500 }
    );
  }
}
