import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const members = await DatabaseService.getProfiles('MEMBER');
    
    const formattedMembers = members.map(profile => ({
      id: profile.id,
      email: profile.email,
      fullName: profile.full_name || 'Tidak ada nama',
      createdAt: profile.created_at,
      assessmentCount: 0, // TODO: Get actual assessment count
      lastActivity: profile.created_at, // Using created_at as placeholder
      isActive: true // Default to active
    }));

    return NextResponse.json(formattedMembers);
  } catch (error) {
    console.error('Failed to fetch members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch members' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('id');

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    // TODO: Implement actual delete functionality
    // await DatabaseService.deleteProfile(memberId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete member:', error);
    return NextResponse.json(
      { error: 'Failed to delete member' },
      { status: 500 }
    );
  }
}
