import { NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function GET() {
  try {
    const memberCount = await DatabaseService.getProfilesCount('MEMBER');
    const tokenCount = await DatabaseService.getTokensCount();
    const usedTokensCount = await DatabaseService.getUsedTokensCount();

    const stats = {
      totalMembers: memberCount,
      totalTokens: tokenCount,
      totalAssessments: usedTokensCount, // Using used tokens as proxy for assessments
      activeTokens: tokenCount - usedTokensCount
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Failed to fetch stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    );
  }
}
