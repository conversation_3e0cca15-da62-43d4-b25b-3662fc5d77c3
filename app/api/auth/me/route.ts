import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/auth";

export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({ error: "Tidak ada session" }, { status: 401 });
    }

    const session = await AuthService.getSession(sessionToken);

    if (!session) {
      return NextResponse.json(
        { error: "Session tidak valid" },
        { status: 401 }
      );
    }

    const profile = await AuthService.getProfile(session.user.id);

    if (!profile) {
      return NextResponse.json(
        { error: "Profile tidak ditemukan" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      user: session.user,
      profile: profile,
    });
  } catch (error) {
    console.error("Me API error:", error);
    return NextResponse.json(
      { error: "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> server" },
      { status: 500 }
    );
  }
}
