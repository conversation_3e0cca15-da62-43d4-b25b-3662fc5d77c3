import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const response = NextResponse.json({
      message: "Logout berhasil",
    });

    // Hapus session cookie
    response.cookies.set("session-token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 0,
      path: "/",
    });

    return response;
  } catch (error) {
    console.error("Signout API error:", error);
    return NextResponse.json(
      { error: "<PERSON><PERSON><PERSON><PERSON> k<PERSON> server" },
      { status: 500 }
    );
  }
}
