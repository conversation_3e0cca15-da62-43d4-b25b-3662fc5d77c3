import { NextRequest, NextResponse } from "next/server";
import { AuthService } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email dan password diperlukan" },
        { status: 400 }
      );
    }

    const result = await AuthService.signIn(email, password);

    if (result.error) {
      return NextResponse.json({ error: result.error }, { status: 401 });
    }

    if (result.user && result.session) {
      // Set HTTP-only cookie untuk session
      const response = NextResponse.json({
        user: result.user,
        message: "Login berhasil",
      });

      response.cookies.set("session-token", result.session.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 24 * 60 * 60, // 24 jam
        path: "/",
      });

      return response;
    }

    return NextResponse.json({ error: "Login gagal" }, { status: 401 });
  } catch (error) {
    console.error("Login API error:", error);
    return NextResponse.json(
      { error: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}
