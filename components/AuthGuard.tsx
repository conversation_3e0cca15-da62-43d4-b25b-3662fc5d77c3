'use client';
import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

interface AuthGuardProps {
  children: React.ReactNode;
  requiredRole?: 'ADMIN' | 'MEMBER';
}

const AuthGuard = ({ children, requiredRole }: AuthGuardProps) => {
  const { user, profile, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to signin if not authenticated
        router.push('/auth/signin');
      } else if (requiredRole && profile?.role !== requiredRole) {
        // Redirect to appropriate dashboard based on role
        const targetPath = profile?.role === 'ADMIN' ? '/admin/dashboard' : '/member/dashboard';
        router.push(targetPath);
      }
    }
  }, [user, profile, loading, requiredRole, router, pathname]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Memuat...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if user is not authenticated or doesn't have required role
  if (!user || (requiredRole && profile?.role !== requiredRole)) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;
