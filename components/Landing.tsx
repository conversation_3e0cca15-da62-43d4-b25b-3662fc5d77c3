import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { GraduationCap, Target, BarChart3, Award } from 'lucide-react';
import Link from 'next/link';

export default function Landing() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-indigo-800" />
        <div className="absolute inset-0 bg-gradient-to-b from-primary/20 to-background/80" />
        
        <div className="relative z-10 container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white drop-shadow-lg">
            <PERSON><PERSON><PERSON>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto text-white/90 drop-shadow-md">
            Temukan jurusan kuliah yang tepat untuk masa depan cerah Anda dengan tes minat bakat RIASEC
          </p>
          <div className="flex gap-4 justify-center flex-wrap">
            <Link href="/auth/signup">
              <Button size="lg" className="shadow-lg hover:scale-105 transition-transform">
                <GraduationCap className="mr-2 h-5 w-5" />
                Mulai Sekarang
              </Button>
            </Link>
            <Link href="/auth/signin">
              <Button variant="outline" size="lg" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                Masuk
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-b from-background to-secondary/10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6">Mengapa Memilih Jurusan Cerdas?</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Platform terpercaya untuk membantu Anda menemukan jurusan yang sesuai dengan minat dan bakat
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <Target className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-center mb-3">Tes RIASEC Akurat</h3>
                <p className="text-muted-foreground text-center">
                  Menggunakan metode RIASEC yang telah terbukti secara ilmiah untuk mengidentifikasi kepribadian dan minat Anda
                </p>
              </CardContent>
            </Card>
            
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <BarChart3 className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-center mb-3">Analisis Mendalam</h3>
                <p className="text-muted-foreground text-center">
                  Dapatkan analisis komprehensif tentang kesesuaian jurusan berdasarkan hasil tes kepribadian Anda
                </p>
              </CardContent>
            </Card>
            
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="pt-6">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-primary/10 rounded-full">
                    <Award className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-center mb-3">Rekomendasi Terpersonalisasi</h3>
                <p className="text-muted-foreground text-center">
                  Terima rekomendasi jurusan yang disesuaikan dengan profil RIASEC dan preferensi karier Anda
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">Siap Menemukan Jurusan Impian Anda?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Bergabunglah dengan ribuan siswa yang telah menemukan jalur karier yang tepat
          </p>
          <Link href="/auth/signup">
            <Button size="lg" variant="secondary" className="shadow-lg hover:scale-105 transition-transform">
              <GraduationCap className="mr-2 h-5 w-5" />
              Daftar Sekarang
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-secondary/5 py-8 border-t">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            © 2024 Jurusan Cerdas. Semua hak dilindungi.
          </p>
        </div>
      </footer>
    </div>
  );
}