/*
  Warnings:

  - Added the required column `password` to the `profiles` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
-- Add password column with default value (bcrypt hash of "admin123")
ALTER TABLE `profiles` ADD COLUMN `password` VARCHAR(191) NOT NULL DEFAULT '$2a$12$LQv3c1yqBWVHxkd0LQ1Mu.VCXVjGHhAAUhJvbGGQHhCF7zFSxp7eS';

-- Update admin password specifically  
UPDATE `profiles` SET `password` = '$2a$12$LQv3c1yqBWVHxkd0LQ1Mu.VCXVjGHhAAUhJvbGGQHhCF7zFSxp7eS' WHERE `email` = '<EMAIL>';

-- Update member password
UPDATE `profiles` SET `password` = '$2a$12$LQv3c1yqBWVHxkd0LQ1Mu.VCXVjGHhAAUhJvbGGQHhCF7zFSxp7eS' WHERE `email` = '<EMAIL>';

-- Remove default after updating existing records
ALTER TABLE `profiles` ALTER COLUMN `password` DROP DEFAULT;
