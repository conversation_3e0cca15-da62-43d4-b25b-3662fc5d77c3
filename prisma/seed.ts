import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // Hash password for all users (admin123)
  const hashedPassword = await bcrypt.hash("admin123", 12);

  // Create admin user
  const adminUser = await prisma.profile.upsert({
    where: { email: "<EMAIL>" },
    update: { password: hashedPassword },
    create: {
      email: "<EMAIL>",
      full_name: "Administrator",
      password: hashedPassword,
      role: "ADMIN",
    },
  });

  console.log("Created admin user:", adminUser);

  // Create a sample token
  const expiredAt = new Date();
  expiredAt.setDate(expiredAt.getDate() + 30); // 30 days from now

  const token = await prisma.token.upsert({
    where: { token: "sample-token-12345" },
    update: {},
    create: {
      token: "sample-token-12345",
      instansi: "SMA Negeri 1",
      description: "Token untuk siswa SMA Negeri 1",
      created_by_id: adminUser.id,
      max_uses: 100,
      current_uses: 0,
      expired_at: expiredAt,
    },
  });

  console.log("Created sample token:", token);

  // Create a test member
  const member = await prisma.profile.upsert({
    where: { email: "<EMAIL>" },
    update: { password: hashedPassword },
    create: {
      email: "<EMAIL>",
      full_name: "Test Member",
      password: hashedPassword,
      role: "MEMBER",
    },
  });

  console.log("Created test member:", member);

  // Create sample assessment result
  const assessmentResult = await prisma.assessmentResult.create({
    data: {
      user_id: member.id,
      scores: JSON.stringify({
        R: 15,
        I: 20,
        A: 10,
        S: 25,
        E: 18,
        C: 12,
      }),
      strengths: JSON.stringify(["Social", "Enterprising", "Investigative"]),
      recommendations: JSON.stringify([
        "Psikologi",
        "Pendidikan",
        "Manajemen",
        "Ilmu Komunikasi",
        "Teknik Informatika",
      ]),
    },
  });

  console.log("Created sample assessment result:", assessmentResult);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
