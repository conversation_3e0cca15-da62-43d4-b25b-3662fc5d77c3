// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  MEMBER
}

model Profile {
  id              String   @id @default(cuid())
  email           String   @unique
  full_name       String?
  password        String   // Hashed password
  role            UserRole @default(MEMBER)
  created_at      DateTime @default(now())
  
  // Relations
  created_tokens  Token[]  @relation("TokenCreator")
  assessment_results AssessmentResult[]
  
  @@map("profiles")
}

model Token {
  id              String    @id @default(cuid())
  token           String    @unique
  instansi        String
  description     String?
  created_by_id   String
  max_uses        Int       @default(1)
  current_uses    Int       @default(0)
  expired_at      DateTime?
  created_at      DateTime  @default(now())
  
  // Relations
  created_by      Profile   @relation("TokenCreator", fields: [created_by_id], references: [id], onDelete: Cascade)
  
  @@map("tokens")
}

model AssessmentResult {
  id              String   @id @default(cuid())
  user_id         String
  scores          Json     // Store RIASEC scores as JSON
  strengths       Json     // Store strengths array as JSON
  recommendations Json     // Store recommendations array as JSON
  created_at      DateTime @default(now())
  
  // Relations
  user            Profile  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  
  @@map("assessment_results")
}
